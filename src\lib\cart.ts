import { createClient } from '@/lib/supabase/client'
import { safeGetUser } from '@/lib/supabase/helpers'
import type { User } from '@supabase/supabase-js'
import { useState, useEffect, useCallback, useRef } from 'react'
import {
  getClientCartSessionId,
  clearClientCartSession,
} from '@/lib/cart-session'

export interface CartItem {
  id: string
  product_id: string
  quantity: number
  product?: {
    id: string
    title: string
    price: number
    discount_price?: number
    images?: string[]
    category: string
    type?: string
    brand?: string
  }
}

export interface Cart {
  id: string
  user_id?: string
  session_id?: string
  items: CartItem[]
  total_amount: number
  created_at: string
  updated_at: string
}

// Client-side cart functions
export class CartManager {
  private supabase = createClient()
  private cache = new Map<string, { data: Cart | null; timestamp: number }>()
  private readonly CACHE_TTL = 30000 // 30 seconds
  private readonly MAX_RETRIES = 3 // Increased retries
  private failureCount = 0
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5 // Increased threshold
  private circuitBreakerOpen = false
  private circuitBreakerResetTime = 0
  private readonly DEBUG_LOGGING = process.env.NODE_ENV === 'development' || process.env.CART_DEBUG === 'true'
  private lastFailureTime = 0
  private consecutiveTimeouts = 0
  private performanceMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    totalResponseTime: 0,
    cacheHits: 0,
    cacheMisses: 0
  }

  private logDebug(...args: unknown[]): void {
    if (this.DEBUG_LOGGING) {
      console.log(...args)
    }
  }

  private recordPerformanceMetrics(responseTime: number, success: boolean, cacheHit: boolean = false): void {
    this.performanceMetrics.totalRequests++
    this.performanceMetrics.totalResponseTime += responseTime
    this.performanceMetrics.averageResponseTime = this.performanceMetrics.totalResponseTime / this.performanceMetrics.totalRequests

    if (success) {
      this.performanceMetrics.successfulRequests++
    } else {
      this.performanceMetrics.failedRequests++
    }

    if (cacheHit) {
      this.performanceMetrics.cacheHits++
    } else {
      this.performanceMetrics.cacheMisses++
    }
  }

  public getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      successRate: this.performanceMetrics.totalRequests > 0
        ? (this.performanceMetrics.successfulRequests / this.performanceMetrics.totalRequests) * 100
        : 0,
      cacheHitRate: (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) > 0
        ? (this.performanceMetrics.cacheHits / (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses)) * 100
        : 0,
      circuitBreakerStatus: this.circuitBreakerOpen ? 'OPEN' : 'CLOSED',
      failureCount: this.failureCount,
      consecutiveTimeouts: this.consecutiveTimeouts
    }
  }

  private isCircuitBreakerOpen(): boolean {
    if (this.circuitBreakerOpen && Date.now() > this.circuitBreakerResetTime) {
      this.circuitBreakerOpen = false
      this.failureCount = 0
      console.log('🛒 CartManager: Circuit breaker reset')
    }
    return this.circuitBreakerOpen
  }

  private recordFailure(error?: unknown): void {
    this.failureCount++
    this.lastFailureTime = Date.now()

    // Track consecutive timeouts separately
    if (error && error.toString().includes('timeout')) {
      this.consecutiveTimeouts++
    } else {
      this.consecutiveTimeouts = 0
    }

    console.warn('🛒 CartManager: Recording failure', this.failureCount, '/', this.CIRCUIT_BREAKER_THRESHOLD,
      'timeouts:', this.consecutiveTimeouts, error)

    // Open circuit breaker if too many failures or consecutive timeouts
    if (this.failureCount >= this.CIRCUIT_BREAKER_THRESHOLD || this.consecutiveTimeouts >= 3) {
      this.circuitBreakerOpen = true
      // Longer reset time for timeout-based failures
      const resetDelay = this.consecutiveTimeouts >= 3 ? 300000 : 120000 // 5 min for timeouts, 2 min for other failures
      this.circuitBreakerResetTime = Date.now() + resetDelay
      console.error('🛒 CartManager: Circuit breaker opened due to repeated failures')
    }
  }

  private recordSuccess(): void {
    if (this.failureCount > 0 || this.consecutiveTimeouts > 0) {
      this.logDebug('🛒 CartManager: Resetting failure count after success')
      this.failureCount = 0
      this.consecutiveTimeouts = 0
    }
  }

  private getCacheKey(userId?: string | null): string {
    return userId ? `user:${userId}` : `session:${this.getSessionId()}`
  }

  private getFromCache(key: string): Cart | null | undefined {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      console.log('🛒 CartManager: Returning cached cart for key:', key)
      this.recordPerformanceMetrics(0, true, true) // Cache hit
      return cached.data
    }
    return undefined
  }

  private setCache(key: string, data: Cart | null): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  // Public method to clear cache when cart is updated
  clearCache(userId?: string): void {
    if (userId) {
      this.cache.delete(`user:${userId}`)
    } else {
      this.cache.delete(`session:${this.getSessionId()}`)
    }
    console.log('🛒 CartManager: Cache cleared for', userId ? `user:${userId}` : 'session')
  }

  // Clear all cache
  clearAllCache(): void {
    this.cache.clear()
    console.log('🛒 CartManager: All cache cleared')
  }

  async getCart(userId?: string): Promise<Cart | null> {
    return this.getCartWithRetry(userId || null, 0)
  }

  private async getCartWithRetry(userId: string | null, attempt: number): Promise<Cart | null> {
    const startTime = Date.now()
    this.logDebug('🛒 CartManager.getCart called with userId:', userId, 'attempt:', attempt + 1)

    // Check circuit breaker
    if (this.isCircuitBreakerOpen()) {
      console.warn('🛒 CartManager: Circuit breaker is open, returning null')
      this.recordPerformanceMetrics(Date.now() - startTime, false)
      return null
    }

    // Check cache first (only on first attempt)
    if (attempt === 0) {
      const cacheKey = this.getCacheKey(userId)
      const cached = this.getFromCache(cacheKey)
      if (cached !== undefined) {
        this.logDebug('🛒 CartManager: Returning cached result')
        return cached
      }
    }

    try {
      let cart: Cart | null = null

      // For guest users, only look for session carts
      if (!userId) {
        const sessionId = this.getSessionId()
        this.logDebug('🛒 CartManager.getCart: Guest user, querying by session_id:', sessionId)
        cart = await this.getCartBySessionId(sessionId)
      } else {
        // For authenticated users, look for user carts
        this.logDebug('🛒 CartManager.getCart: Authenticated user, querying by user_id:', userId)
        cart = await this.getCartByUserId(userId)

        // If no user cart found, check for session cart to potentially merge
        if (!cart) {
          const sessionId = this.getSessionId()
          this.logDebug('🛒 CartManager.getCart: No user cart, checking session cart:', sessionId)
          cart = await this.getCartBySessionId(sessionId)
          if (cart) {
            this.logDebug('🛒 CartManager.getCart: Found session cart, should merge with user account')
            // TODO: Implement cart merging logic here if needed
          }
        }
      }

      // Cache the result
      const cacheKey = this.getCacheKey(userId)
      this.setCache(cacheKey, cart)
      this.recordSuccess()

      const responseTime = Date.now() - startTime
      this.recordPerformanceMetrics(responseTime, true, false) // Cache miss but successful
      this.logDebug('🛒 CartManager.getCart: Result:', cart ? 'Cart found' : 'No cart found', 'in', responseTime, 'ms')
      return cart
    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error('🛒 CartManager.getCart: Error in getCart (attempt', attempt + 1, ') after', responseTime, 'ms:', error)

      // Retry logic with exponential backoff
      if (attempt < this.MAX_RETRIES && !this.isCircuitBreakerOpen()) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000) // Max 5 second delay
        this.logDebug('🛒 CartManager: Retrying in', delay, 'ms')

        await new Promise(resolve => setTimeout(resolve, delay))
        return this.getCartWithRetry(userId, attempt + 1)
      }

      this.recordPerformanceMetrics(responseTime, false)
      this.recordFailure(error)
      return null
    }
  }

  private async getCartByUserId(userId: string): Promise<Cart | null> {
    const startTime = Date.now()

    try {
      this.logDebug('🛒 CartManager.getCartByUserId: Querying user cart for userId:', userId)

      // Use single optimized query with join to reduce round trips
      const queryWithTimeout = Promise.race([
        this.supabase
          .from('carts')
          .select(`
            *,
            cart_items (
              *,
              products (
                id,
                title,
                price,
                discount_price,
                images,
                category,
                coffee_type,
                brand
              )
            )
          `)
          .eq('user_id', userId)
          .eq('status', 'active')
          .order('updated_at', { ascending: false })
          .limit(1)
          .maybeSingle(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Database query timeout')), 15000) // Increased timeout
        )
      ])

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: cartData, error } = await queryWithTimeout as any

      const queryTime = Date.now() - startTime
      this.logDebug('🛒 CartManager.getCartByUserId: Query completed in', queryTime, 'ms')

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found - this is expected, not an error
          this.logDebug('🛒 CartManager.getCartByUserId: No cart found for user')
          return null
        }
        console.warn('🛒 CartManager.getCartByUserId: Database error:', error)
        return null
      }

      if (!cartData) {
        this.logDebug('🛒 CartManager.getCartByUserId: No cart data returned')
        return null
      }

      // Transform cart items to match Cart interface
      const items = cartData.cart_items?.map((item: {
        id: string
        product_id: string
        quantity: number
        products: {
          id: string
          title: string
          price: number
          discount_price?: number
          images?: string[]
          category: string
          coffee_type?: string
          brand?: string
        }
      }) => ({
        ...item,
        product: item.products ? {
          ...item.products,
          type: item.products.coffee_type
        } : undefined
      })) || []

      const cart: Cart = {
        ...cartData,
        items
      }

      this.logDebug('🛒 CartManager.getCartByUserId: Cart loaded with', items.length, 'items in', Date.now() - startTime, 'ms')
      return cart
    } catch (error) {
      const queryTime = Date.now() - startTime
      // Only log unexpected database errors, not auth-related ones
      if (error instanceof Error &&
          !error.message.includes('Auth session missing') &&
          !error.message.includes('Invalid Refresh Token') &&
          !error.message.includes('refresh_token_not_found')) {
        console.warn('🛒 CartManager.getCartByUserId: Error after', queryTime, 'ms:', error)
      }
      return null
    }
  }

  private async getCartBySessionId(sessionId: string): Promise<Cart | null> {
    const startTime = Date.now()

    try {
      this.logDebug('🛒 CartManager.getCartBySessionId: Querying session cart for:', sessionId)

      // Use single optimized query with join to reduce round trips
      const queryWithTimeout = Promise.race([
        this.supabase
          .from('carts')
          .select(`
            *,
            cart_items (
              *,
              products (
                id,
                title,
                price,
                discount_price,
                images,
                category,
                coffee_type,
                brand
              )
            )
          `)
          .eq('session_id', sessionId)
          .is('user_id', null)
          .eq('status', 'active')
          .order('updated_at', { ascending: false })
          .limit(1)
          .maybeSingle(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Database query timeout')), 15000) // Increased timeout
        )
      ])

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: cartData, error } = await queryWithTimeout as any

      const queryTime = Date.now() - startTime
      this.logDebug('🛒 CartManager.getCartBySessionId: Query completed in', queryTime, 'ms')

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found - this is expected, not an error
          this.logDebug('🛒 CartManager.getCartBySessionId: No cart found for session')
          return null
        }
        console.warn('🛒 CartManager.getCartBySessionId: Database error:', error)
        return null
      }

      if (!cartData) {
        this.logDebug('🛒 CartManager.getCartBySessionId: No cart data returned')
        return null
      }

      // Transform cart items to match Cart interface
      const items = cartData.cart_items?.map((item: {
        id: string
        product_id: string
        quantity: number
        products: {
          id: string
          title: string
          price: number
          discount_price?: number
          images?: string[]
          category: string
          coffee_type?: string
          brand?: string
        }
      }) => ({
        ...item,
        product: item.products ? {
          ...item.products,
          type: item.products.coffee_type
        } : undefined
      })) || []

      const cart: Cart = {
        ...cartData,
        items
      }

      this.logDebug('🛒 CartManager.getCartBySessionId: Cart loaded with', items.length, 'items in', Date.now() - startTime, 'ms')
      return cart
    } catch (error) {
      const queryTime = Date.now() - startTime
      // Only log unexpected database errors, not auth-related ones
      if (error instanceof Error &&
          !error.message.includes('Auth session missing') &&
          !error.message.includes('Invalid Refresh Token') &&
          !error.message.includes('refresh_token_not_found')) {
        console.warn('🛒 CartManager.getCartBySessionId: Error after', queryTime, 'ms:', error)
      }
      return null
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private transformCartData(data: any): Cart {
    console.log('🛒 CartManager.transformCartData: Transforming cart data...')
    // Transform the data to match our Cart interface
    const cart: Cart = {
      ...data,
      items: data.cart_items?.map((item: {
        id: string
        product_id: string
        quantity: number
        products: {
          id: string
          title: string
          price: number
          discount_price?: number
          images?: string[]
          category: string
          coffee_type?: string
          brand?: string
        }
      }) => ({
        ...item,
        product: item.products ? {
          ...item.products,
          type: item.products.coffee_type
        } : undefined
      })) || []
    }

    console.log('🛒 CartManager.transformCartData: Cart transformed successfully:', {
      cartId: cart.id,
      itemsCount: cart.items.length
    })
    return cart

  }

  async addToCart(productId: string, quantity: number = 1, userId?: string): Promise<boolean> {
    try {
      let cart = await this.getCart(userId)

      if (!cart) {
        // Create new cart
        const cartData: {
          status: string
          total_amount: number
          user_id?: string
          session_id?: string
        } = {
          status: 'active',
          total_amount: 0
        }

        if (userId) {
          cartData.user_id = userId
        } else {
          cartData.session_id = this.getSessionId()
        }

        const { data: newCart, error: cartError } = await this.supabase
          .from('carts')
          .insert(cartData)
          .select()
          .single()

        if (cartError) {
          console.error('Error creating cart:', cartError)
          return false
        }

        cart = { ...newCart, items: [] }
      }

      // At this point cart is guaranteed to exist
      if (!cart) {
        console.error('Cart is null after creation')
        return false
      }

      // Check if item already exists in cart
      const existingItem = cart.items.find(item => item.product_id === productId)

      if (existingItem) {
        // Update quantity
        const { error } = await this.supabase
          .from('cart_items')
          .update({ quantity: existingItem.quantity + quantity })
          .eq('id', existingItem.id)

        if (error) {
          console.error('Error updating cart item:', error)
          return false
        }
      } else {
        // Add new item
        const { error } = await this.supabase
          .from('cart_items')
          .insert({
            cart_id: cart.id,
            product_id: productId,
            quantity
          })

        if (error) {
          console.error('Error adding cart item:', error)
          return false
        }
      }

      // Update cart total
      await this.updateCartTotal(cart.id)
      return true
    } catch (error) {
      console.error('Error in addToCart:', error)
      return false
    }
  }

  async updateCartItem(itemId: string, quantity: number): Promise<boolean> {
    try {
      if (quantity <= 0) {
        return await this.removeFromCart(itemId)
      }

      const { error } = await this.supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', itemId)

      if (error) {
        console.error('Error updating cart item:', error)
        return false
      }

      // Get cart_id to update total
      const { data: item } = await this.supabase
        .from('cart_items')
        .select('cart_id')
        .eq('id', itemId)
        .maybeSingle()

      if (item) {
        await this.updateCartTotal(item.cart_id)
      }

      return true
    } catch (error) {
      console.error('Error in updateCartItem:', error)
      return false
    }
  }

  async removeFromCart(itemId: string): Promise<boolean> {
    try {
      // Get cart_id before deleting
      const { data: item } = await this.supabase
        .from('cart_items')
        .select('cart_id')
        .eq('id', itemId)
        .maybeSingle()

      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId)

      if (error) {
        console.error('Error removing cart item:', error)
        return false
      }

      if (item) {
        await this.updateCartTotal(item.cart_id)
      }

      return true
    } catch (error) {
      console.error('Error in removeFromCart:', error)
      return false
    }
  }

  async clearCart(userId?: string): Promise<boolean> {
    try {
      const cart = await this.getCart(userId)
      if (!cart) return true

      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cart.id)

      if (error) {
        console.error('Error clearing cart:', error)
        return false
      }

      await this.updateCartTotal(cart.id)
      return true
    } catch (error) {
      console.error('Error in clearCart:', error)
      return false
    }
  }

  private async updateCartTotal(cartId: string): Promise<void> {
    try {
      // Calculate total from cart items
      const { data: items } = await this.supabase
        .from('cart_items')
        .select(`
          quantity,
          products (price, discount_price)
        `)
        .eq('cart_id', cartId)

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const total = items?.reduce((sum: number, item: any) => {
        const price = item.products?.discount_price || item.products?.price || 0
        return sum + (price * item.quantity)
      }, 0) || 0

      await this.supabase
        .from('carts')
        .update({ 
          total_amount: total,
          updated_at: new Date().toISOString()
        })
        .eq('id', cartId)
    } catch (error) {
      console.error('Error updating cart total:', error)
    }
  }

  private getSessionId(): string {
    // Always use client-side session ID generation for consistency
    // Server-side session handling should be done separately in Server Components
    return getClientCartSessionId()
  }

  // Clear the current session and create a new one
  async clearSession(): Promise<void> {
    console.log('🛒 CartManager.clearSession: Clearing session and creating new one')
    // Always use client-side session clearing for consistency
    clearClientCartSession()
  }
}

// Singleton CartManager instance
let cartManagerInstance: CartManager | null = null

function getCartManager(): CartManager {
  if (!cartManagerInstance) {
    cartManagerInstance = new CartManager()
  }
  return cartManagerInstance
}

// React hook for cart management
export function useCart() {
  const [cart, setCart] = useState<Cart>({
    id: '',
    items: [],
    total_amount: 0,
    created_at: '',
    updated_at: ''
  })
  const [loading, setLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const cartManager = getCartManager()
  const refreshingRef = useRef(false) // Use ref to track refreshing state

  const refreshCart = useCallback(async () => {
    // Prevent multiple simultaneous calls using a ref instead of state
    if (refreshingRef.current) {
      console.log('🛒 useCart: refreshCart already in progress, skipping')
      return
    }

    console.log('🛒 useCart: refreshCart called')
    refreshingRef.current = true
    setIsRefreshing(true)
    setLoading(true)

    try {
      // Get current user to pass to getCart with timeout
      const debugLogging = process.env.NODE_ENV === 'development' || process.env.CART_DEBUG === 'true'
      if (debugLogging) console.log('🛒 useCart: Getting user...')

      const supabase = createClient()

      // Add timeout to prevent hanging
      const getUserWithTimeout = () => {
        return Promise.race([
          safeGetUser(supabase),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('getUser timeout')), 8000) // Increased timeout
          )
        ])
      }

      let user: User | null = null
      try {
        const authUser = await getUserWithTimeout() as User | null
        user = authUser
        if (debugLogging) console.log('🛒 useCart: User for cart refresh:', { userId: user?.id, isAuthenticated: !!user })
      } catch (authError) {
        // Only log unexpected auth errors, not normal guest user scenarios
        if (authError instanceof Error &&
            !authError.message.includes('Auth session missing') &&
            !authError.message.includes('Invalid Refresh Token') &&
            !authError.message.includes('getUser timeout')) {
          console.error('🛒 useCart: Unexpected auth error:', authError)
        }
        // Continue with null user (guest mode) - don't throw error
        user = null
      }

      if (debugLogging) console.log('🛒 useCart: Calling cartManager.getCart...')
      // CartManager now has built-in timeout, retry, and circuit breaker logic
      const cartData = await cartManager.getCart(user?.id)
      if (debugLogging) console.log('🛒 useCart: Cart data retrieved:', {
        cartExists: !!cartData,
        cartId: cartData?.id,
        itemsCount: cartData?.items?.length || 0
      })

      if (cartData) {
        if (debugLogging) console.log('🛒 useCart: Setting cart data')
        setCart(cartData)
      } else {
        if (debugLogging) console.log('🛒 useCart: No cart found')

        // If we're not authenticated and no session cart exists, clear session to start fresh
        if (!user) {
          if (debugLogging) console.log('🛒 useCart: Guest user with no cart, clearing session')
          await cartManager.clearSession()
        }

        // Set empty cart
        setCart({
          id: '',
          items: [],
          total_amount: 0,
          created_at: '',
          updated_at: ''
        })
      }
      if (debugLogging) console.log('🛒 useCart: refreshCart completed successfully')
    } catch (error) {
      console.error('🛒 useCart: Error refreshing cart:', error)
      // Set empty cart on error
      setCart({
        id: '',
        items: [],
        total_amount: 0,
        created_at: '',
        updated_at: ''
      })
    } finally {
      const debugLogging = process.env.NODE_ENV === 'development' || process.env.CART_DEBUG === 'true'
      if (debugLogging) console.log('🛒 useCart: Setting loading to false')
      refreshingRef.current = false
      setLoading(false)
      setIsRefreshing(false)
    }
  }, [cartManager]) // Stable dependencies to prevent infinite loops

  useEffect(() => {
    refreshCart()
  }, [refreshCart])

  const addToCart = async (productId: string, quantity: number = 1) => {
    const success = await cartManager.addToCart(productId, quantity)
    if (success) {
      // Clear cache before refreshing
      cartManager.clearAllCache()
      await refreshCart()
    }
    return success
  }

  const updateCartItem = async (itemId: string, quantity: number) => {
    const success = await cartManager.updateCartItem(itemId, quantity)
    if (success) {
      // Clear cache before refreshing
      cartManager.clearAllCache()
      await refreshCart()
    }
    return success
  }

  const removeFromCart = async (itemId: string) => {
    const success = await cartManager.removeFromCart(itemId)
    if (success) {
      // Clear cache before refreshing
      cartManager.clearAllCache()
      await refreshCart()
    }
    return success
  }

  const clearCart = async () => {
    const success = await cartManager.clearCart()
    if (success) {
      // Clear cache before refreshing
      cartManager.clearAllCache()
      await refreshCart()
    }
    return success
  }

  return {
    cart,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart
  }
}
