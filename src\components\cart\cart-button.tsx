'use client'

import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ShoppingCart } from 'lucide-react'
import { CartManager, type Cart } from '@/lib/cart'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'

export function CartButton() {
  const [cart, setCart] = useState<Cart | null>(null)
  const [, setIsLoading] = useState(true)
  const isLoadingRef = useRef(false) // Prevent race conditions using ref
  const t = useTranslations('navigation')
  const locale = useLocale()

  const cartManager = useMemo(() => new CartManager(), [])
  const supabase = useMemo(() => createClient(), [])

  const loadCart = useCallback(async (userId?: string) => {
    // Prevent multiple simultaneous loads
    if (isLoadingRef.current) {
      // Don't log this as it's normal behavior - just return silently
      return
    }

    try {
      isLoadingRef.current = true
      setIsLoading(true)

      // CartManager now has built-in timeout and retry logic
      const cartData = await cartManager.getCart(userId)
      setCart(cartData)
    } catch (error) {
      // Only log unexpected errors, not auth-related ones for guest users
      if (error instanceof Error &&
          !error.message.includes('Auth session missing') &&
          !error.message.includes('Invalid Refresh Token')) {
        console.error('🛒 CartButton: Error loading cart:', error)
      }
      // Set empty cart on error to prevent UI issues
      setCart(null)
    } finally {
      setIsLoading(false)
      isLoadingRef.current = false
    }
  }, [cartManager]) // Removed isLoadingRef dependency to prevent infinite loops

  useEffect(() => {
    let mounted = true

    // Initial load with error handling
    const initialLoad = async () => {
      if (!mounted) return
      try {
        await loadCart()
      } catch (error) {
        // Only log unexpected errors for initial load
        if (error instanceof Error &&
            !error.message.includes('Auth session missing') &&
            !error.message.includes('Invalid Refresh Token')) {
          console.error('Initial cart load failed:', error)
        }
      }
    }

    initialLoad()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return
        try {
          await loadCart(session?.user?.id)
        } catch (error) {
          // Only log unexpected errors for auth state changes
          if (error instanceof Error &&
              !error.message.includes('Auth session missing') &&
              !error.message.includes('Invalid Refresh Token')) {
            console.error('Auth state change cart load failed:', error)
          }
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [loadCart, supabase.auth])

  const itemCount = cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0

  return (
    <Button variant="ghost" size="icon" asChild className="relative overflow-visible">
      <Link href={`/${locale}/cart`} prefetch={false}>
        <ShoppingCart className="h-5 w-5" />
        {itemCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
          >
            {itemCount > 99 ? '99+' : itemCount}
          </Badge>
        )}
        <span className="sr-only">
          {t('cart')} {itemCount > 0 && `(${itemCount} Artikel)`}
        </span>
      </Link>
    </Button>
  )
}
