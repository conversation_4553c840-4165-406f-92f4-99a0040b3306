// Cart V2 TanStack Query Hooks
// Server state management for cart operations with optimistic updates

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { safeGetUser } from '@/lib/supabase/helpers'
import { CartService } from './service'
import type { User } from '@supabase/supabase-js'
import { useCartStore, useOptimisticItems, useCartErrorActions } from './store'
import { calculateCartSummary, createOptimisticCartItem } from './utils'
import { getErrorMessage } from './errors'
import type {
  Cart,
  AddToCartParams,
  UpdateCartItemParams,
  RemoveFromCartParams,
  CartErrorCode,
} from './types'

// Initialize cart service
const cartService = new CartService()

/**
 * Custom hook for getting current user
 */
function useUser() {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let mounted = true
    let loadingTimeout: NodeJS.Timeout
    const supabase = createClient()

    // Get initial user with timeout protection
    const loadUser = async () => {
      try {
        // Add timeout to prevent hanging
        const userPromise = safeGetUser(supabase)
        const timeoutPromise = new Promise<User | null>((_, reject) =>
          setTimeout(() => reject(new Error('User load timeout')), 5000)
        )

        const currentUser = await Promise.race([userPromise, timeoutPromise])
        if (mounted) {
          setUser(currentUser)
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Error loading user:', error)
        if (mounted) {
          setUser(null)
          setIsLoading(false)
        }
      }
    }

    // Debounce initial load to prevent rapid calls
    loadingTimeout = setTimeout(loadUser, 100)

    // Listen for auth changes with debouncing
    let authChangeTimeout: NodeJS.Timeout
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_, session) => {
        if (mounted) {
          // Debounce auth changes to prevent rapid updates
          clearTimeout(authChangeTimeout)
          authChangeTimeout = setTimeout(() => {
            if (mounted) {
              setUser(session?.user ?? null)
              setIsLoading(false)
            }
          }, 50)
        }
      }
    )

    return () => {
      mounted = false
      clearTimeout(loadingTimeout)
      clearTimeout(authChangeTimeout)
      subscription.unsubscribe()
    }
  }, [])

  return { user, isLoading }
}

/**
 * Query hook for fetching cart data
 */
export function useCartQuery() {
  const { user, isLoading: userLoading } = useUser()
  const userId = user?.id

  return useQuery({
    queryKey: userId
      ? ['cart', 'user', userId]
      : ['cart', 'session'],
    queryFn: () => cartService.getCart(),
    enabled: !userLoading, // Don't run query until user state is resolved
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error: unknown) => {
      // Don't retry on client errors (4xx)
      const errorObj = error as { status?: number }
      if (errorObj?.status && errorObj.status >= 400 && errorObj.status < 500) {
        return false
      }
      return failureCount < 3
    },
  })
}

/**
 * Query hook for cart summary calculations
 */
export function useCartSummary() {
  const { data: cart } = useCartQuery()
  const { items: optimisticItems } = useOptimisticItems()

  // Merge real cart with optimistic items for calculations
  const cartWithOptimistic = cart ? {
    ...cart,
    items: [...cart.items, ...optimisticItems]
  } : null

  return {
    data: calculateCartSummary(cartWithOptimistic),
    cart: cartWithOptimistic,
  }
}

/**
 * Mutation hook for adding items to cart
 */
export function useAddToCartMutation() {
  const queryClient = useQueryClient()
  const { user } = useUser()
  const { addItem: addOptimisticItem, clearItems } = useOptimisticItems()
  const { setLastAddedItem, setDrawerOpen } = useCartStore()
  const { showError } = useCartErrorActions()

  return useMutation({
    mutationFn: (params: AddToCartParams) => 
      cartService.addToCart({ ...params, user_id: user?.id }),

    onMutate: async (variables) => {
      const { product_id, quantity } = variables

      // Cancel outgoing refetches
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      await queryClient.cancelQueries({ queryKey })

      // Snapshot previous value
      const previousCart = queryClient.getQueryData<Cart>(queryKey)

      // Create optimistic item
      const optimisticItem = createOptimisticCartItem(product_id, quantity)
      addOptimisticItem(optimisticItem)

      // Return context for rollback
      return { previousCart, optimisticItem }
    },

    onSuccess: (data) => {
      // Clear optimistic items on success
      clearItems()
      
      // Set last added item for UI feedback
      setLastAddedItem(data)
      
      // Open cart drawer
      setDrawerOpen(true)

      // Invalidate and refetch cart
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      queryClient.invalidateQueries({ queryKey })
    },

    onError: (error: unknown) => {
      // Clear optimistic items on error
      clearItems()

      // Show error message
      const errorObj = error as { code?: string }
      const errorMessage = errorObj.code
        ? getErrorMessage(errorObj.code as CartErrorCode)
        : 'Failed to add item to cart'

      showError(errorMessage)
    },
  })
}

/**
 * Mutation hook for updating cart item quantity
 */
export function useUpdateCartItemMutation() {
  const queryClient = useQueryClient()
  const { user } = useUser()
  const { updateItem: updateOptimisticItem } = useOptimisticItems()
  const { showError } = useCartErrorActions()

  return useMutation({
    mutationFn: (params: UpdateCartItemParams) => 
      cartService.updateCartItem(params),

    onMutate: async (variables) => {
      const { item_id, quantity } = variables

      // Cancel outgoing refetches
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      await queryClient.cancelQueries({ queryKey })

      // Snapshot previous value
      const previousCart = queryClient.getQueryData<Cart>(queryKey)

      // Optimistically update the cart
      if (previousCart) {
        const updatedCart = {
          ...previousCart,
          items: previousCart.items.map(item =>
            item.id === item_id
              ? { ...item, quantity, updated_at: new Date().toISOString() }
              : item
          )
        }
        queryClient.setQueryData(queryKey, updatedCart)
      }

      // Update optimistic items if any
      updateOptimisticItem(item_id, quantity)

      return { previousCart }
    },

    onError: (error: unknown) => {
      // Show error message
      const errorMessage = (error as { code?: string }).code
        ? getErrorMessage((error as { code?: string }).code as CartErrorCode)
        : 'Failed to update cart item'

      showError(errorMessage)
    },

    onSettled: () => {
      // Refetch cart data
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      queryClient.invalidateQueries({ queryKey })
    },
  })
}

/**
 * Mutation hook for removing items from cart
 */
export function useRemoveFromCartMutation() {
  const queryClient = useQueryClient()
  const { user } = useUser()
  const { removeItem: removeOptimisticItem } = useOptimisticItems()
  const { showError } = useCartErrorActions()

  return useMutation({
    mutationFn: (params: RemoveFromCartParams) => 
      cartService.removeFromCart(params),

    onMutate: async (variables) => {
      const { item_id } = variables

      // Cancel outgoing refetches
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      await queryClient.cancelQueries({ queryKey })

      // Snapshot previous value
      const previousCart = queryClient.getQueryData<Cart>(queryKey)

      // Optimistically remove the item
      if (previousCart) {
        const updatedCart = {
          ...previousCart,
          items: previousCart.items.filter(item => item.id !== item_id)
        }
        queryClient.setQueryData(queryKey, updatedCart)
      }

      // Remove from optimistic items if any
      removeOptimisticItem(item_id)

      return { previousCart }
    },

    onError: (error: unknown) => {
      // Show error message
      const errorMessage = (error as { code?: string }).code
        ? getErrorMessage((error as { code?: string }).code as CartErrorCode)
        : 'Failed to remove cart item'

      showError(errorMessage)
    },

    onSettled: () => {
      // Refetch cart data
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      queryClient.invalidateQueries({ queryKey })
    },
  })
}

/**
 * Mutation hook for clearing entire cart
 */
export function useClearCartMutation() {
  const queryClient = useQueryClient()
  const { user } = useUser()
  const { clearItems } = useOptimisticItems()
  const { showError } = useCartErrorActions()

  return useMutation({
    mutationFn: () => cartService.clearCart(),

    onMutate: async () => {
      // Cancel outgoing refetches
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      await queryClient.cancelQueries({ queryKey })

      // Snapshot previous value
      const previousCart = queryClient.getQueryData<Cart>(queryKey)

      // Optimistically clear the cart
      if (previousCart) {
        const clearedCart = {
          ...previousCart,
          items: [],
          total_amount: 0,
        }
        queryClient.setQueryData(queryKey, clearedCart)
      }

      // Clear optimistic items
      clearItems()

      return { previousCart }
    },

    onError: (error: unknown) => {
      // Show error message
      const errorMessage = (error as { code?: string }).code
        ? getErrorMessage((error as { code?: string }).code as CartErrorCode)
        : 'Failed to clear cart'

      showError(errorMessage)
    },

    onSettled: () => {
      // Refetch cart data
      const queryKey = user?.id 
        ? ['cart', 'user', user.id] 
        : ['cart', 'session']
      
      queryClient.invalidateQueries({ queryKey })
    },
  })
}

/**
 * Mutation hook for transferring guest cart to user account
 */
export function useTransferGuestCartMutation() {
  const queryClient = useQueryClient()
  const { showError } = useCartErrorActions()

  return useMutation({
    mutationFn: ({ sessionId }: { userId: string; sessionId?: string }) =>
      cartService.syncGuestCartToUser(sessionId || ''),

    onSuccess: (data, variables) => {
      // Invalidate both session and user cart queries
      queryClient.invalidateQueries({ queryKey: ['cart', 'session'] })
      queryClient.invalidateQueries({ queryKey: ['cart', 'user', variables.userId] })
    },

    onError: (error: unknown) => {
      const errorMessage = (error as { code?: string }).code
        ? getErrorMessage((error as { code?: string }).code as CartErrorCode)
        : 'Failed to transfer cart'
      
      showError(errorMessage)
    },
  })
}
