'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Coffee, Home, Search, Zap, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { useRouter, useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'

export default function LocaleNotFound() {
  const router = useRouter()
  const params = useParams()
  const locale = params?.locale as string || 'de'
  const t = useTranslations('notFound')

  return (
    <div className="min-h-[80vh] flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <Card className="border-2 border-dashed border-primary/20">
          <CardContent className="p-12">
            {/* Fun Coffee Animation */}
            <div className="relative mb-8">
              <div className="flex justify-center items-center space-x-2">
                <Coffee className="h-16 w-16 text-primary animate-bounce" />
                <div className="text-6xl font-bold text-primary">4</div>
                <Coffee className="h-16 w-16 text-primary animate-bounce delay-100" />
                <div className="text-6xl font-bold text-primary">4</div>
              </div>
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping"></div>
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping delay-75"></div>
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping delay-150"></div>
                </div>
              </div>
            </div>

            {/* Fun Headlines */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-amber-600 font-semibold mb-6">
              {t('subtitle')}
            </p>

            {/* Witty Description */}
            <div className="bg-amber-50 rounded-lg p-6 mb-8">
              <p className="text-gray-700 mb-4">
                {t('description')}
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-amber-700">
                <Zap className="h-4 w-4" />
                <span>{t('tip')}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  asChild
                >
                  <Link href={`/${locale}`}>
                    <Home className="mr-2 h-5 w-5" />
                    {t('goHome')}
                  </Link>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  asChild
                >
                  <Link href={`/${locale}/coffee-box-builder`}>
                    <Coffee className="mr-2 h-5 w-5" />
                    {t('coffeeBoxBuilder')}
                  </Link>
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  variant="outline" 
                  onClick={() => router.back()}
                  className="border-gray-300"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Zurück
                </Button>

                <Button
                  variant="outline"
                  className="border-gray-300"
                  asChild
                >
                  <Link href={`/${locale}/shop`}>
                    <Search className="mr-2 h-4 w-4" />
                    {t('searchShop')}
                  </Link>
                </Button>
              </div>
            </div>

            {/* Fun Footer */}
            <div className="mt-8 pt-6 border-t border-amber-200">
              <p className="text-sm text-muted-foreground">
                {t('errorCode')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
