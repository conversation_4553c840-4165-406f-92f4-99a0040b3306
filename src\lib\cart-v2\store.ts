// Cart V2 Zustand Store
// Client-side state management for cart UI and optimistic updates

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { CartStore, CartItem } from './types'

export const useCartStore = create<CartStore>()(
  devtools(
    (set, get) => ({
      // State
      isDrawerOpen: false,
      isLoading: false,
      lastAddedItem: null,
      optimisticItems: [],
      error: null,

      // Actions
      setDrawerOpen: (open: boolean) => {
        // Only update if we're on the client side to prevent hydration issues
        if (typeof window !== 'undefined') {
          set({ isDrawerOpen: open }, false, 'setDrawerOpen')
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading }, false, 'setLoading')
      },

      setLastAddedItem: (item: CartItem | null) => {
        set({ lastAddedItem: item }, false, 'setLastAddedItem')
      },

      addOptimisticItem: (item: CartItem) => {
        const { optimisticItems } = get()
        
        // Check if item with same product already exists
        const existingIndex = optimisticItems.findIndex(
          existing => existing.product_id === item.product_id
        )

        if (existingIndex >= 0) {
          // Update existing item quantity
          const updatedItems = [...optimisticItems]
          updatedItems[existingIndex] = {
            ...updatedItems[existingIndex],
            quantity: updatedItems[existingIndex].quantity + item.quantity,
            updated_at: new Date().toISOString(),
          }
          set({ optimisticItems: updatedItems }, false, 'addOptimisticItem/update')
        } else {
          // Add new item
          set(
            { optimisticItems: [...optimisticItems, item] },
            false,
            'addOptimisticItem/new'
          )
        }
      },

      removeOptimisticItem: (itemId: string) => {
        const { optimisticItems } = get()
        set(
          {
            optimisticItems: optimisticItems.filter(item => item.id !== itemId)
          },
          false,
          'removeOptimisticItem'
        )
      },

      updateOptimisticItem: (itemId: string, quantity: number) => {
        const { optimisticItems } = get()
        const updatedItems = optimisticItems.map(item =>
          item.id === itemId
            ? {
                ...item,
                quantity,
                updated_at: new Date().toISOString(),
              }
            : item
        )
        set({ optimisticItems: updatedItems }, false, 'updateOptimisticItem')
      },

      clearOptimisticItems: () => {
        set({ optimisticItems: [] }, false, 'clearOptimisticItems')
      },

      setError: (error: string | null) => {
        set({ error }, false, 'setError')
      },

      clearError: () => {
        set({ error: null }, false, 'clearError')
      },
    }),
    {
      name: 'cart-store',
      // Only enable devtools in development
      enabled: process.env.NODE_ENV === 'development',
    }
  )
)

// Selectors for better performance with hydration safety
export const useCartDrawer = () => {
  const store = useCartStore(state => ({
    isOpen: state.isDrawerOpen,
    setOpen: state.setDrawerOpen,
  }))

  // Prevent hydration mismatch by ensuring drawer is closed on server
  return {
    ...store,
    isOpen: typeof window === 'undefined' ? false : store.isOpen
  }
}

export const useCartLoading = () => useCartStore(state => ({
  isLoading: state.isLoading,
  setLoading: state.setLoading,
}))

export const useCartError = () => useCartStore(state => ({
  error: state.error,
  setError: state.setError,
  clearError: state.clearError,
}))

export const useOptimisticItems = () => useCartStore(state => ({
  items: state.optimisticItems,
  addItem: state.addOptimisticItem,
  removeItem: state.removeOptimisticItem,
  updateItem: state.updateOptimisticItem,
  clearItems: state.clearOptimisticItems,
}))

export const useLastAddedItem = () => useCartStore(state => ({
  item: state.lastAddedItem,
  setItem: state.setLastAddedItem,
}))

// Helper hooks for common patterns
export const useCartDrawerActions = () => {
  const { setOpen } = useCartDrawer()

  return {
    openDrawer: () => setOpen(true),
    closeDrawer: () => setOpen(false),
    toggleDrawer: () => {
      // Prevent hydration issues by checking client-side
      if (typeof window !== 'undefined') {
        setOpen(!useCartStore.getState().isDrawerOpen)
      }
    },
  }
}

export const useCartErrorActions = () => {
  const { setError, clearError } = useCartError()
  
  return {
    showError: (message: string) => setError(message),
    hideError: () => clearError(),
  }
}
