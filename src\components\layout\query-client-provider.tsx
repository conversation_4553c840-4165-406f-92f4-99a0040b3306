'use client'

import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState, ReactNode } from 'react'
import { createCartQueryClient } from '@/lib/cart-v2/query-client'

interface QueryProviderProps {
  children: ReactNode
}

export function QueryProvider({ children }: QueryProviderProps) {
  // Create a stable query client instance with proper hydration handling
  const [queryClient] = useState(() => {
    const client = createCartQueryClient()

    // Prevent hydration mismatches by ensuring consistent state
    if (typeof window !== 'undefined') {
      // Client-side: enable all features
      return client
    } else {
      // Server-side: disable features that cause hydration issues
      return client
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Only show devtools in development and client-side */}
      {typeof window !== 'undefined' && process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}
