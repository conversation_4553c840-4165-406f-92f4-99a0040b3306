import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Currency formatting for Swiss market (CHF)
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('de-CH', {
    style: 'currency',
    currency: 'CHF'
  }).format(amount)
}

// Date formatting for Swiss market (dd/mm/yyyy)
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('de-CH', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date).replace(/\./g, '/')
}

// DateTime formatting for Swiss market
export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('de-CH', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj).replace(/\./g, '/')
}

// Calculate cost per espresso (for coffee products)
export function calculateCostPerEspresso(
  price: number,
  coffeeType: string,
  packQuantity?: number,
  packWeightGrams?: number,
  gramsPerEspresso?: number
): number {
  if (price <= 0) return 0

  // For capsules and pods, use pack quantity directly
  if (coffeeType === 'capsules' || coffeeType === 'pods') {
    if (!packQuantity || packQuantity <= 0) return 0
    return price / packQuantity
  }

  // For beans and ground coffee, calculate based on weight
  if (coffeeType === 'beans' || coffeeType === 'ground') {
    if (!packWeightGrams || !gramsPerEspresso || packWeightGrams <= 0 || gramsPerEspresso <= 0) return 0
    const servingsPerPack = packWeightGrams / gramsPerEspresso
    return price / servingsPerPack
  }

  return 0
}

// Generate URL-friendly slug
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Swiss postal code validation
export function isValidSwissPostalCode(postalCode: string): boolean {
  const swissPostalRegex = /^[1-9]\d{3}$/
  return swissPostalRegex.test(postalCode)
}

// Calculate discount percentage
export function calculateDiscountPercentage(originalPrice: number, discountedPrice: number): number {
  if (originalPrice <= 0 || discountedPrice >= originalPrice) return 0
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
}

// Calculate points earned (1 CHF = 1 point, with optional multiplier)
export function calculatePointsEarned(amount: number, multiplier: number = 1): number {
  return Math.floor(amount * multiplier)
}

// Determine user level based on points
export function determineUserLevel(points: number, levels?: Array<{ level: number; minimum_points: number }>): number | string {
  if (!levels) {
    // Default string-based levels for backward compatibility
    if (points >= 10000) return 'Platinum'
    if (points >= 5000) return 'Gold'
    if (points >= 2000) return 'Silver'
    return 'Bronze'
  }

  // Find the highest level the user qualifies for
  let userLevel = levels[0]?.level || 1
  for (const level of levels) {
    if (points >= level.minimum_points) {
      userLevel = level.level
    } else {
      break
    }
  }
  return userLevel
}

// Calculate shipping cost based on country and rates
export function calculateShippingCost(
  country: string,
  subtotal: number,
  shippingRates?: Array<{ country: string; cost: number; free_shipping_threshold: number }>
): number {
  if (!shippingRates) {
    // Default Swiss rates for backward compatibility
    if (subtotal >= 90) return 0
    return 8.50
  }

  const rate = shippingRates.find(r => r.country === country)
  if (!rate) return 0 // Unknown country

  if (subtotal >= rate.free_shipping_threshold) return 0
  return rate.cost
}

// Generate coupon code
export function generateCouponCode(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Validate coupon code format
export function isValidCouponCode(code: string): boolean {
  return /^[A-Z0-9]{4,12}$/.test(code)
}

// Swiss VAT calculations (7.7% standard rate)
const DEFAULT_VAT_RATE = 0.077

export function calculateVATFromInclusive(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return inclusiveAmount - (inclusiveAmount / (1 + vatRate))
}

export function calculateExclusiveFromInclusive(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return inclusiveAmount / (1 + vatRate)
}

export function calculateInclusiveFromExclusive(exclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return exclusiveAmount * (1 + vatRate)
}

export interface VATBreakdown {
  inclusive: number
  exclusive: number
  vatAmount: number
  vatRate: number
}

export function formatVATBreakdown(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): VATBreakdown {
  const exclusive = calculateExclusiveFromInclusive(inclusiveAmount, vatRate)
  const vatAmount = calculateVATFromInclusive(inclusiveAmount, vatRate)

  return {
    inclusive: Math.round(inclusiveAmount * 100) / 100,
    exclusive: Math.round(exclusive * 100) / 100,
    vatAmount: Math.round(vatAmount * 100) / 100,
    vatRate
  }
}
